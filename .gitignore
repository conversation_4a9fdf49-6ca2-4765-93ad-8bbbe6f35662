# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/
pip-log.txt
pip-delete-this-directory.txt
.pytest_cache/
.coverage
*.cover
.hypothesis/
*.egg-info/
dist/
# Build outputs (but allow our build/ directory)
build/
!build/

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Environment variables
.env
.env.local
.env.*.local

# Build outputs

/dist
/out
/.next
.cache/

# Testing
coverage/
.nyc_output/

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Documentation build
docs/_build/
site/

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Google Cloud
.gcloudignore.venv/
