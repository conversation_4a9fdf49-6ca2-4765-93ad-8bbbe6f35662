groups:
  # Service Health Alerts
  - name: service_health
    interval: 30s
    rules:
      - alert: ServiceDown
        expr: up{job="ccl-services"} == 0
        for: 2m
        labels:
          severity: critical
          team: platform
          component: infrastructure
        annotations:
          summary: "Service {{ $labels.service_name }} is down"
          description: "{{ $labels.service_name }} in {{ $labels.environment }} has been down for more than 2 minutes"
          runbook: "https://docs.ccl.io/runbooks/service-down"
          dashboard: "https://grafana.ccl.io/d/ccl-service-overview"
      
      - alert: HighErrorRate
        expr: |
          sum(rate(http_requests_total{status=~"5.."}[5m])) by (service_name, environment)
          /
          sum(rate(http_requests_total[5m])) by (service_name, environment)
          > 0.05
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High error rate for {{ $labels.service_name }}"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.service_name }} in {{ $labels.environment }}"
          runbook: "https://docs.ccl.io/runbooks/high-error-rate"
          dashboard: "https://grafana.ccl.io/d/ccl-service-overview"
      
      - alert: CriticalErrorRate
        expr: |
          sum(rate(http_requests_total{status=~"5.."}[5m])) by (service_name, environment)
          /
          sum(rate(http_requests_total[5m])) by (service_name, environment)
          > 0.10
        for: 2m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Critical error rate for {{ $labels.service_name }}"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.service_name }} in {{ $labels.environment }}"
          runbook: "https://docs.ccl.io/runbooks/high-error-rate"
          dashboard: "https://grafana.ccl.io/d/ccl-service-overview"
      
      - alert: HighLatency
        expr: |
          histogram_quantile(0.95,
            sum(rate(http_request_duration_seconds_bucket[5m])) by (service_name, environment, le)
          ) > 0.5
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High latency for {{ $labels.service_name }}"
          description: "P95 latency is {{ $value }}s for {{ $labels.service_name }} in {{ $labels.environment }}"
          runbook: "https://docs.ccl.io/runbooks/high-latency"
          dashboard: "https://grafana.ccl.io/d/ccl-service-overview"
      
      - alert: ExtremeLatency
        expr: |
          histogram_quantile(0.95,
            sum(rate(http_request_duration_seconds_bucket[5m])) by (service_name, environment, le)
          ) > 2
        for: 2m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Extreme latency for {{ $labels.service_name }}"
          description: "P95 latency is {{ $value }}s for {{ $labels.service_name }} in {{ $labels.environment }}"
          runbook: "https://docs.ccl.io/runbooks/high-latency"
          dashboard: "https://grafana.ccl.io/d/ccl-service-overview"

  # Infrastructure Alerts
  - name: infrastructure
    interval: 30s
    rules:
      - alert: HighMemoryUsage
        expr: |
          container_memory_usage_bytes{pod=~"ccl-.*"}
          /
          container_spec_memory_limit_bytes{pod=~"ccl-.*"}
          > 0.9
        for: 5m
        labels:
          severity: warning
          team: platform
          component: infrastructure
        annotations:
          summary: "High memory usage for {{ $labels.pod }}"
          description: "Memory usage is {{ $value | humanizePercentage }} for pod {{ $labels.pod }}"
          runbook: "https://docs.ccl.io/runbooks/high-memory-usage"
      
      - alert: MemoryOOM
        expr: |
          container_memory_usage_bytes{pod=~"ccl-.*"}
          /
          container_spec_memory_limit_bytes{pod=~"ccl-.*"}
          > 0.98
        for: 1m
        labels:
          severity: critical
          team: platform
          component: infrastructure
        annotations:
          summary: "Pod {{ $labels.pod }} is about to OOM"
          description: "Memory usage is {{ $value | humanizePercentage }} for pod {{ $labels.pod }}"
          runbook: "https://docs.ccl.io/runbooks/memory-oom"
      
      - alert: HighCPUUsage
        expr: |
          avg(rate(container_cpu_usage_seconds_total{pod=~"ccl-.*"}[5m])) by (pod)
          > 0.8
        for: 10m
        labels:
          severity: warning
          team: platform
          component: infrastructure
        annotations:
          summary: "High CPU usage for {{ $labels.pod }}"
          description: "CPU usage is {{ $value | humanizePercentage }} for pod {{ $labels.pod }}"
          runbook: "https://docs.ccl.io/runbooks/high-cpu-usage"
      
      - alert: PodCrashLooping
        expr: |
          rate(kube_pod_container_status_restarts_total{pod=~"ccl-.*"}[15m]) > 0
        for: 5m
        labels:
          severity: critical
          team: platform
          component: infrastructure
        annotations:
          summary: "Pod {{ $labels.pod }} is crash looping"
          description: "Pod {{ $labels.pod }} has restarted {{ $value }} times in the last 15 minutes"
          runbook: "https://docs.ccl.io/runbooks/pod-crash-looping"
      
      - alert: DiskSpaceLow
        expr: |
          (node_filesystem_free_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"})
          < 0.1
        for: 5m
        labels:
          severity: warning
          team: platform
          component: infrastructure
        annotations:
          summary: "Low disk space on {{ $labels.instance }}"
          description: "Only {{ $value | humanizePercentage }} disk space remaining on {{ $labels.instance }}"
          runbook: "https://docs.ccl.io/runbooks/disk-space-low"

  # SLO Alerts
  - name: slo_alerts
    interval: 30s
    rules:
      - alert: SLOBurnRateHigh
        expr: |
          sum(rate(http_requests_total{status=~"5.."}[5m])) by (service_name)
          /
          sum(rate(http_requests_total[5m])) by (service_name)
          / (1 - 0.999) > 2
        for: 5m
        labels:
          severity: warning
          team: platform
          slo: availability
        annotations:
          summary: "High error budget burn rate for {{ $labels.service_name }}"
          description: "Error budget is burning {{ $value }}x faster than normal for {{ $labels.service_name }}"
          runbook: "https://docs.ccl.io/runbooks/slo-burn-rate"
          dashboard: "https://grafana.ccl.io/d/ccl-slo-dashboard"
      
      - alert: SLOBurnRateCritical
        expr: |
          sum(rate(http_requests_total{status=~"5.."}[5m])) by (service_name)
          /
          sum(rate(http_requests_total[5m])) by (service_name)
          / (1 - 0.999) > 10
        for: 2m
        labels:
          severity: critical
          team: platform
          slo: availability
        annotations:
          summary: "Critical error budget burn rate for {{ $labels.service_name }}"
          description: "Error budget is burning {{ $value }}x faster than normal for {{ $labels.service_name }}"
          runbook: "https://docs.ccl.io/runbooks/slo-burn-rate"
          dashboard: "https://grafana.ccl.io/d/ccl-slo-dashboard"
      
      - alert: LatencySLOViolation
        expr: |
          (sum(rate(http_request_duration_seconds_bucket{le="0.1"}[30m])) by (service_name)
          /
          sum(rate(http_request_duration_seconds_count[30m])) by (service_name))
          < 0.95
        for: 10m
        labels:
          severity: warning
          team: platform
          slo: latency
        annotations:
          summary: "Latency SLO violation for {{ $labels.service_name }}"
          description: "Only {{ $value | humanizePercentage }} of requests are meeting the 100ms latency target"
          runbook: "https://docs.ccl.io/runbooks/latency-slo-violation"
          dashboard: "https://grafana.ccl.io/d/ccl-slo-dashboard"

  # Business-Specific Alerts
  - name: business_alerts
    interval: 30s
    rules:
      - alert: QueryAccuracyLow
        expr: |
          avg(query_confidence_score) by (environment)
          < 0.7
        for: 10m
        labels:
          severity: warning
          team: ai
          component: query-intelligence
        annotations:
          summary: "Query accuracy is low"
          description: "Average query confidence score is {{ $value }} in {{ $labels.environment }}"
          runbook: "https://docs.ccl.io/runbooks/query-accuracy-low"
          dashboard: "https://grafana.ccl.io/d/ccl-query-intelligence"
      
      - alert: PatternDetectionStalled
        expr: |
          sum(increase(patterns_detected_total[1h])) by (environment)
          == 0
        for: 1h
        labels:
          severity: warning
          team: ml
          component: pattern-mining
        annotations:
          summary: "Pattern detection has stalled"
          description: "No patterns detected in the last hour in {{ $labels.environment }}"
          runbook: "https://docs.ccl.io/runbooks/pattern-detection-stalled"
      
      - alert: PaymentFailureRateHigh
        expr: |
          sum(rate(payment_errors_total[5m])) by (provider)
          /
          sum(rate(purchase_attempts_total[5m]))
          > 0.05
        for: 5m
        labels:
          severity: critical
          team: marketplace
          component: payments
        annotations:
          summary: "High payment failure rate"
          description: "Payment failure rate is {{ $value | humanizePercentage }} for provider {{ $labels.provider }}"
          runbook: "https://docs.ccl.io/runbooks/payment-failures"
      
      - alert: VertexAIQuotaExhaustion
        expr: |
          sum(rate(vertex_ai_requests_total[5m])) by (model)
          > 0.9
        for: 5m
        labels:
          severity: warning
          team: ai
          component: vertex-ai
        annotations:
          summary: "Approaching Vertex AI quota limit"
          description: "Vertex AI request rate for {{ $labels.model }} is at {{ $value }} requests/second"
          runbook: "https://docs.ccl.io/runbooks/vertex-ai-quota"

  # Security Alerts
  - name: security_alerts
    interval: 30s
    rules:
      - alert: AuthenticationFailureSpike
        expr: |
          sum(increase(login_attempts_total{success="false"}[5m])) by (method)
          > 100
        for: 2m
        labels:
          severity: warning
          team: security
          component: authentication
        annotations:
          summary: "Authentication failure spike detected"
          description: "{{ $value }} failed login attempts in the last 5 minutes for method {{ $labels.method }}"
          runbook: "https://docs.ccl.io/runbooks/auth-failure-spike"
      
      - alert: UnusualTrafficPattern
        expr: |
          abs(sum(rate(http_requests_total[5m])) - sum(rate(http_requests_total[5m] offset 1h)))
          /
          sum(rate(http_requests_total[5m] offset 1h))
          > 3
        for: 10m
        labels:
          severity: warning
          team: security
          component: traffic
        annotations:
          summary: "Unusual traffic pattern detected"
          description: "Traffic has changed by {{ $value | humanizePercentage }} compared to 1 hour ago"
          runbook: "https://docs.ccl.io/runbooks/unusual-traffic"

  # Data Pipeline Alerts
  - name: data_pipeline_alerts
    interval: 30s
    rules:
      - alert: AnalysisPipelineBacklog
        expr: |
          sum(active_analyses) by (environment)
          > 100
        for: 10m
        labels:
          severity: warning
          team: platform
          component: analysis-engine
        annotations:
          summary: "Analysis pipeline backlog"
          description: "{{ $value }} analyses are currently active in {{ $labels.environment }}"
          runbook: "https://docs.ccl.io/runbooks/analysis-backlog"
      
      - alert: EmbeddingGenerationSlow
        expr: |
          histogram_quantile(0.95,
            sum(rate(embedding_generation_duration_seconds_bucket[5m])) by (le)
          ) > 5
        for: 10m
        labels:
          severity: warning
          team: ai
          component: embeddings
        annotations:
          summary: "Embedding generation is slow"
          description: "P95 embedding generation time is {{ $value }}s"
          runbook: "https://docs.ccl.io/runbooks/embedding-slow"