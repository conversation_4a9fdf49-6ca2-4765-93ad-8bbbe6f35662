# SLO Definitions for CCL Platform
# These SLOs define our reliability targets and are used to calculate error budgets

slos:
  # Platform-Wide SLOs
  - name: api-availability
    service: all
    description: "Overall API availability across all services"
    sli:
      type: availability
      good_events: 'http_requests_total{status!~"5.."}'
      total_events: 'http_requests_total'
    objectives:
      - target: 0.999  # 99.9%
        window: 30d
        name: "Monthly availability"
      - target: 0.995  # 99.5%
        window: 7d
        name: "Weekly availability"
      - target: 0.99   # 99%
        window: 1d
        name: "Daily availability"
    error_budget_policies:
      - name: "Freeze non-critical deployments"
        threshold: 0.5  # When 50% of error budget is consumed
        duration: 24h
      - name: "All hands on deck"
        threshold: 0.2  # When 80% of error budget is consumed
        duration: "until_resolved"
    
  - name: api-latency
    service: all
    description: "API response time for user-facing endpoints"
    sli:
      type: latency
      threshold: 100ms
      metric: 'http_request_duration_seconds'
      aggregation: 'histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))'
    objectives:
      - target: 0.95   # 95% of requests under 100ms
        window: 30d
        name: "Monthly latency"
      - target: 0.90   # 90% of requests under 100ms
        window: 7d
        name: "Weekly latency"
    
  # Service-Specific SLOs
  - name: analysis-engine-performance
    service: analysis-engine
    description: "Repository analysis completion time"
    sli:
      type: latency
      threshold: 300s  # 5 minutes
      metric: 'analysis_completion_duration_seconds'
      aggregation: 'histogram_quantile(0.95, sum(rate(analysis_completion_duration_seconds_bucket[1h])) by (le))'
    objectives:
      - target: 0.95   # 95% of analyses complete within 5 minutes
        window: 30d
        name: "Analysis performance"
        
  - name: query-intelligence-accuracy
    service: query-intelligence
    description: "Query response accuracy based on confidence scores"
    sli:
      type: custom
      good_events: 'queries_total{confidence>="0.8"}'
      total_events: 'queries_total'
    objectives:
      - target: 0.85   # 85% of queries with high confidence
        window: 30d
        name: "Query accuracy"
      - target: 0.80   # 80% of queries with high confidence
        window: 7d
        name: "Weekly query accuracy"
    
  - name: query-intelligence-latency
    service: query-intelligence
    description: "Query response time"
    sli:
      type: latency
      threshold: 100ms
      metric: 'query_processing_duration'
      aggregation: 'histogram_quantile(0.95, sum(rate(query_processing_duration_bucket[5m])) by (le))'
    objectives:
      - target: 0.95   # 95% of queries respond within 100ms
        window: 30d
        name: "Query latency"
        
  - name: pattern-detection-accuracy
    service: pattern-mining
    description: "Pattern detection precision"
    sli:
      type: custom
      good_events: 'pattern_validation_results_total{result="correct"}'
      total_events: 'pattern_validation_results_total'
    objectives:
      - target: 0.90   # 90% pattern accuracy
        window: 30d
        name: "Pattern accuracy"
        
  - name: marketplace-transaction-success
    service: marketplace
    description: "Successful marketplace transactions"
    sli:
      type: availability
      good_events: 'purchase_attempts_total{success="true"}'
      total_events: 'purchase_attempts_total'
    objectives:
      - target: 0.999  # 99.9% transaction success
        window: 30d
        name: "Transaction success"
      - target: 0.995  # 99.5% transaction success
        window: 1d
        name: "Daily transaction success"
        
  - name: marketplace-api-availability
    service: marketplace
    description: "Marketplace API availability"
    sli:
      type: availability
      good_events: 'http_requests_total{service_name="marketplace", status!~"5.."}'
      total_events: 'http_requests_total{service_name="marketplace"}'
    objectives:
      - target: 0.9999  # 99.99% availability for marketplace
        window: 30d
        name: "Marketplace availability"
        
  # Infrastructure SLOs
  - name: database-availability
    service: infrastructure
    description: "Database connection availability"
    sli:
      type: availability
      good_events: 'database_connections_successful_total'
      total_events: 'database_connections_attempted_total'
    objectives:
      - target: 0.999  # 99.9% database availability
        window: 30d
        name: "Database availability"
        
  - name: cache-hit-rate
    service: infrastructure
    description: "Cache effectiveness"
    sli:
      type: custom
      good_events: 'cache_hits_total'
      total_events: 'sum(cache_hits_total + cache_misses_total)'
    objectives:
      - target: 0.80   # 80% cache hit rate
        window: 30d
        name: "Cache effectiveness"
        
  # AI/ML SLOs
  - name: vertex-ai-availability
    service: ai-infrastructure
    description: "Vertex AI API availability"
    sli:
      type: availability
      good_events: 'vertex_ai_requests_total{status="success"}'
      total_events: 'vertex_ai_requests_total'
    objectives:
      - target: 0.99   # 99% Vertex AI availability
        window: 30d
        name: "Vertex AI availability"
        
  - name: ml-model-performance
    service: ai-infrastructure
    description: "ML model inference latency"
    sli:
      type: latency
      threshold: 200ms
      metric: 'ml_inference_duration'
      aggregation: 'histogram_quantile(0.95, sum(rate(ml_inference_duration_bucket[5m])) by (le))'
    objectives:
      - target: 0.90   # 90% of inferences under 200ms
        window: 30d
        name: "Model performance"

# Error Budget Policies
error_budget_policies:
  - name: standard-policy
    description: "Standard error budget policy for all services"
    thresholds:
      - remaining_budget: 0.75  # 75% remaining
        actions:
          - type: notification
            target: sre-team
            message: "25% of error budget consumed"
      - remaining_budget: 0.50  # 50% remaining
        actions:
          - type: restriction
            target: deployments
            restriction: "Only critical fixes allowed"
          - type: notification
            target: engineering-leads
            message: "50% of error budget consumed - deployment restrictions in place"
      - remaining_budget: 0.25  # 25% remaining
        actions:
          - type: restriction
            target: deployments
            restriction: "No deployments except emergency fixes"
          - type: escalation
            target: engineering-director
            message: "75% of error budget consumed - urgent action required"
      - remaining_budget: 0.10  # 10% remaining
        actions:
          - type: incident
            severity: critical
            message: "90% of error budget consumed - all hands on deck"
          - type: restriction
            target: all
            restriction: "Complete deployment freeze"

# SLO Review Schedule
review_schedule:
  - frequency: weekly
    participants:
      - sre-team
      - service-owners
    agenda:
      - Current SLO status
      - Error budget consumption
      - Incidents impact
      - SLO adjustments needed
      
  - frequency: monthly
    participants:
      - sre-team
      - engineering-leads
      - product-managers
    agenda:
      - SLO achievement review
      - Customer impact analysis
      - SLO target adjustments
      - Error budget policy effectiveness
      
  - frequency: quarterly
    participants:
      - sre-team
      - engineering-director
      - cto
    agenda:
      - Overall reliability trends
      - SLO strategy review
      - Investment priorities
      - Customer satisfaction correlation