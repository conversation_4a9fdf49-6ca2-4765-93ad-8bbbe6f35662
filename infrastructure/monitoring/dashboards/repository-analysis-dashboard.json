{"dashboard": {"id": null, "uid": "ccl-repository-analysis", "title": "CCL Repository Analysis Dashboard", "tags": ["ccl", "analysis-engine", "performance"], "timezone": "browser", "schemaVersion": 30, "version": 1, "refresh": "30s", "time": {"from": "now-6h", "to": "now"}, "templating": {"list": [{"name": "datasource", "type": "datasource", "query": "prometheus", "current": {"text": "CCL-Prometheus", "value": "CCL-Prometheus"}}, {"name": "environment", "type": "query", "datasource": "$datasource", "query": "label_values(analysis_requests_total, environment)", "current": {"text": "production", "value": "production"}, "multi": false}, {"name": "language", "type": "query", "datasource": "$datasource", "query": "label_values(analysis_requests_total{environment=\"$environment\"}, language)", "current": {"text": "All", "value": "$__all"}, "multi": true, "includeAll": true}]}, "panels": [{"title": "Analysis Request Rate", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}, "targets": [{"expr": "sum(rate(analysis_requests_total{environment=\"$environment\", language=~\"$language\"}[5m]))", "refId": "A"}], "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "orientation": "auto", "text": {}, "textMode": "auto", "graphMode": "area", "justifyMode": "auto"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps", "decimals": 2}}}, {"title": "Active Analyses", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}, "targets": [{"expr": "sum(active_analyses{environment=\"$environment\"})", "refId": "A"}], "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "orientation": "auto", "text": {}, "textMode": "auto", "graphMode": "area", "justifyMode": "auto"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}, "unit": "short"}}}, {"title": "Patterns Detected (1h)", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}, "targets": [{"expr": "sum(increase(patterns_detected_total{environment=\"$environment\", language=~\"$language\"}[1h]))", "refId": "A"}], "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "orientation": "auto", "text": {}, "textMode": "auto", "graphMode": "area", "justifyMode": "auto"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short", "decimals": 0}}}, {"title": "Analysis Success Rate", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}, "targets": [{"expr": "(1 - (sum(rate(errors_total{environment=\"$environment\", type=\"analysis_error\"}[5m])) / sum(rate(analysis_requests_total{environment=\"$environment\"}[5m])))) * 100", "refId": "A"}], "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "orientation": "auto", "text": {}, "textMode": "auto", "graphMode": "area", "justifyMode": "auto"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}, "unit": "percent", "decimals": 2, "min": 0, "max": 100}}}, {"title": "Analysis Requests by Language", "type": "piechart", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 4}, "targets": [{"expr": "sum(increase(analysis_requests_total{environment=\"$environment\", language=~\"$language\"}[1h])) by (language)", "legendFormat": "{{language}}", "refId": "A"}], "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "donut", "displayLabels": ["name", "percent"], "legend": {"displayMode": "table", "placement": "right", "values": ["value", "percent"]}}}, {"title": "File Processing Duration by Language", "type": "graph", "gridPos": {"h": 8, "w": 16, "x": 8, "y": 4}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(file_processing_duration_seconds_bucket{environment=\"$environment\", language=~\"$language\"}[5m])) by (language, le))", "legendFormat": "P95 - {{language}}", "refId": "A"}, {"expr": "histogram_quantile(0.50, sum(rate(file_processing_duration_seconds_bucket{environment=\"$environment\", language=~\"$language\"}[5m])) by (language, le))", "legendFormat": "P50 - {{language}}", "refId": "B"}], "yaxes": [{"format": "s", "label": "Processing Time", "show": true, "decimals": 2}, {"show": false}], "legend": {"show": true, "alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "values": true}}, {"title": "AST Parsing Performance", "type": "heatmap", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "targets": [{"expr": "sum(increase(file_processing_duration_seconds_bucket{environment=\"$environment\", language=~\"$language\"}[1m])) by (le)", "format": "heatmap", "legendFormat": "{{le}}", "refId": "A"}], "options": {"calculate": false, "yAxis": {"axisPlacement": "left", "decimals": 2, "unit": "s"}, "cellGap": 1, "color": {"scheme": "Oranges", "mode": "scheme"}}, "dataFormat": "tsbuckets"}, {"title": "Memory Usage by Analysis Instance", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "targets": [{"expr": "avg(container_memory_usage_bytes{namespace=\"ccl-$environment\", pod=~\"analysis-engine-.*\"}) by (pod)", "legendFormat": "{{pod}}", "refId": "A"}], "yaxes": [{"format": "bytes", "label": "Memory Usage", "show": true}, {"show": false}], "thresholds": [{"value": 3221225472, "op": "gt", "fill": true, "line": true, "colorMode": "warning"}, {"value": 4294967296, "op": "gt", "fill": true, "line": true, "colorMode": "critical"}]}, {"title": "Repository Size Distribution", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "targets": [{"expr": "sum(increase(analysis_requests_total{environment=\"$environment\", language=~\"$language\"}[1h])) by (size_category)", "legendFormat": "{{size_category}}", "refId": "A"}], "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom"}}, "fieldConfig": {"defaults": {"custom": {"drawStyle": "bars", "barAlignment": 0}}}}, {"title": "Pattern Detection Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "targets": [{"expr": "sum(rate(patterns_detected_total{environment=\"$environment\", language=~\"$language\"}[5m])) by (language)", "legendFormat": "{{language}}", "refId": "A"}], "yaxes": [{"format": "short", "label": "Patterns/sec", "show": true}, {"show": false}], "legend": {"show": true, "alignAsTable": true, "avg": true, "current": true, "max": true, "values": true}}, {"title": "Analysis Pipeline Stages", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 28}, "targets": [{"expr": "avg(file_parsing_duration_seconds{environment=\"$environment\"})", "legendFormat": "Parsing", "refId": "A"}, {"expr": "avg(ast_analysis_duration_seconds{environment=\"$environment\"})", "legendFormat": "AST Analysis", "refId": "B"}, {"expr": "avg(pattern_detection_duration_seconds{environment=\"$environment\"})", "legendFormat": "Pattern Detection", "refId": "C"}, {"expr": "avg(embedding_generation_duration_seconds{environment=\"$environment\"})", "legendFormat": "Embedding Generation", "refId": "D"}, {"expr": "avg(storage_write_duration_seconds{environment=\"$environment\"})", "legendFormat": "Storage Write", "refId": "E"}], "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.1}, {"color": "red", "value": 0.5}]}, "unit": "s", "decimals": 3}}}]}}