name: Deploy Service
on:
  workflow_call:
    inputs:
      service_name:
        required: true
        type: string
      environment:
        required: true
        type: string
      version:
        required: true
        type: string
      rollout_strategy:
        required: false
        type: string
        default: "canary"
      initial_traffic_percentage:
        required: false
        type: number
        default: 10
    secrets:
      GCP_SA_KEY:
        required: true

jobs:
  pre-deployment-checks:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    outputs:
      deployment_id: ${{ steps.create_deployment.outputs.deployment_id }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Verify image exists
        run: |
          gcloud container images describe us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ inputs.version }}

      - name: Check environment health
        run: |
          # Verify target environment is healthy
          make health-check ENV=${{ inputs.environment }}

      - name: Create deployment record
        id: create_deployment
        run: |
          DEPLOYMENT_ID=$(uuidgen)
          echo "deployment_id=$DEPLOYMENT_ID" >> $GITHUB_OUTPUT
          
          # Record deployment start
          echo "${{ inputs.service_name }},${{ inputs.version }},${{ inputs.environment }},STARTED,$(date -u +%Y-%m-%dT%H:%M:%SZ),$DEPLOYMENT_ID" >> deployments.csv
          gsutil cp deployments.csv gs://ccl-deployments/history/

  deploy:
    runs-on: ubuntu-latest
    needs: pre-deployment-checks
    environment: ${{ inputs.environment }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy to Cloud Run (Blue-Green)
        if: inputs.rollout_strategy == 'blue-green'
        run: |
          # Deploy new revision without traffic
          gcloud run deploy ${{ inputs.service_name }} \
            --image=us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ inputs.version }} \
            --no-traffic \
            --tag=${{ inputs.version }} \
            --region=us-central1 \
            --platform=managed \
            --memory=2Gi \
            --cpu=2 \
            --max-instances=100 \
            --min-instances=1 \
            --set-env-vars="VERSION=${{ inputs.version }},ENVIRONMENT=${{ inputs.environment }}"

      - name: Deploy to Cloud Run (Canary)
        if: inputs.rollout_strategy == 'canary'
        run: |
          # Deploy new revision with initial traffic
          gcloud run deploy ${{ inputs.service_name }} \
            --image=us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ inputs.version }} \
            --tag=${{ inputs.version }} \
            --region=us-central1 \
            --platform=managed \
            --memory=2Gi \
            --cpu=2 \
            --max-instances=100 \
            --min-instances=1 \
            --set-env-vars="VERSION=${{ inputs.version }},ENVIRONMENT=${{ inputs.environment }}"
          
          # Gradually shift traffic
          gcloud run services update-traffic ${{ inputs.service_name }} \
            --to-tags=${{ inputs.version }}=${{ inputs.initial_traffic_percentage }} \
            --region=us-central1

      - name: Deploy to Cloud Run (Rolling)
        if: inputs.rollout_strategy == 'rolling'
        run: |
          # Standard rolling deployment
          gcloud run deploy ${{ inputs.service_name }} \
            --image=us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ inputs.version }} \
            --region=us-central1 \
            --platform=managed \
            --memory=2Gi \
            --cpu=2 \
            --max-instances=100 \
            --min-instances=1 \
            --set-env-vars="VERSION=${{ inputs.version }},ENVIRONMENT=${{ inputs.environment }}"

      - name: Wait for deployment stabilization
        run: |
          echo "Waiting for deployment to stabilize..."
          sleep 60

  post-deployment-validation:
    runs-on: ubuntu-latest
    needs: [pre-deployment-checks, deploy]
    environment: ${{ inputs.environment }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Run smoke tests
        run: |
          make smoke-test ENV=${{ inputs.environment }} SERVICE=${{ inputs.service_name }}

      - name: Monitor metrics for 5 minutes
        run: |
          echo "Monitoring deployment metrics..."
          python scripts/monitor_deployment.py \
            --service=${{ inputs.service_name }} \
            --version=${{ inputs.version }} \
            --environment=${{ inputs.environment }} \
            --duration=300

      - name: Check error rates
        id: check_errors
        run: |
          ERROR_RATE=$(gcloud logging read \
            "resource.type=cloud_run_revision AND resource.labels.service_name=${{ inputs.service_name }} AND severity>=ERROR" \
            --limit=100 \
            --format="value(timestamp)" \
            --freshness=5m | wc -l)
          
          echo "error_rate=$ERROR_RATE" >> $GITHUB_OUTPUT
          
          if [ "$ERROR_RATE" -gt "5" ]; then
            echo "High error rate detected: $ERROR_RATE errors in 5 minutes"
            exit 1
          fi

      - name: Rollback on failure
        if: failure()
        run: |
          echo "Deployment validation failed, initiating rollback..."
          ./scripts/deploy/rollback.sh ${{ inputs.service_name }} ${{ inputs.environment }}

      - name: Complete canary rollout
        if: success() && inputs.rollout_strategy == 'canary'
        run: |
          # Gradually increase traffic: 10% -> 50% -> 100%
          echo "Increasing traffic to 50%..."
          gcloud run services update-traffic ${{ inputs.service_name }} \
            --to-tags=${{ inputs.version }}=50 \
            --region=us-central1
          
          sleep 300  # Wait 5 minutes
          
          echo "Completing rollout to 100%..."
          gcloud run services update-traffic ${{ inputs.service_name }} \
            --to-tags=${{ inputs.version }}=100 \
            --region=us-central1

      - name: Complete blue-green rollout
        if: success() && inputs.rollout_strategy == 'blue-green'
        run: |
          echo "Switching traffic to new version..."
          gcloud run services update-traffic ${{ inputs.service_name }} \
            --to-tags=${{ inputs.version }}=100 \
            --region=us-central1

      - name: Update deployment record
        if: always()
        run: |
          STATUS=${{ job.status == 'success' && 'SUCCESS' || 'FAILED' }}
          echo "${{ inputs.service_name }},${{ inputs.version }},${{ inputs.environment }},$STATUS,$(date -u +%Y-%m-%dT%H:%M:%SZ),${{ needs.pre-deployment-checks.outputs.deployment_id }}" >> deployments.csv
          gsutil cp deployments.csv gs://ccl-deployments/history/

      - name: Send deployment notification
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          text: |
            Deployment ${{ job.status }}: ${{ inputs.service_name }}:${{ inputs.version }} to ${{ inputs.environment }}
            Strategy: ${{ inputs.rollout_strategy }}
            Error Rate: ${{ steps.check_errors.outputs.error_rate }} errors/5min
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
