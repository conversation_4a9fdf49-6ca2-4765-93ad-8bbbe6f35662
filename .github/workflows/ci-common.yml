name: CI Common
on:
  workflow_call:
    inputs:
      service_name:
        required: true
        type: string
      language:
        required: true
        type: string
      working_directory:
        required: false
        type: string
        default: "."
    secrets:
      GCP_SA_KEY:
        required: true
      SONAR_TOKEN:
        required: true

jobs:
  quality-checks:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ inputs.working_directory }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for better analysis

      - name: Set up language environment
        uses: ./.github/actions/setup-${{ inputs.language }}
        with:
          service_name: ${{ inputs.service_name }}

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo
            ~/.cache/pip
            ~/go/pkg/mod
            ~/.npm
            node_modules
            target
          key: ${{ runner.os }}-${{ inputs.language }}-${{ hashFiles('**/Cargo.lock', '**/requirements.txt', '**/go.mod', '**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-${{ inputs.language }}-

      - name: Install dependencies
        run: make deps
        env:
          SERVICE_NAME: ${{ inputs.service_name }}
          LANGUAGE: ${{ inputs.language }}

      - name: Lint code
        run: make lint
        env:
          SERVICE_NAME: ${{ inputs.service_name }}
          LANGUAGE: ${{ inputs.language }}

      - name: Security scan
        run: make security-scan
        env:
          SERVICE_NAME: ${{ inputs.service_name }}
          LANGUAGE: ${{ inputs.language }}

      - name: Run tests
        run: make test-ci
        env:
          CI: true
          SERVICE_NAME: ${{ inputs.service_name }}
          LANGUAGE: ${{ inputs.language }}

      - name: Generate coverage report
        run: make coverage
        env:
          SERVICE_NAME: ${{ inputs.service_name }}
          LANGUAGE: ${{ inputs.language }}

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage.xml
          flags: ${{ inputs.service_name }}
          name: ${{ inputs.service_name }}-coverage
          fail_ci_if_error: true

      - name: SonarQube analysis
        uses: sonarsource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          projectBaseDir: ${{ inputs.working_directory }}
          args: >
            -Dsonar.projectKey=ccl-${{ inputs.service_name }}
            -Dsonar.organization=ccl-platform
            -Dsonar.sources=.
            -Dsonar.exclusions=**/*_test.go,**/*.test.ts,**/test_*.py,**/tests.rs,**/target/**,**/node_modules/**

      - name: Build artifacts
        run: make build
        env:
          SERVICE_NAME: ${{ inputs.service_name }}
          LANGUAGE: ${{ inputs.language }}
          VERSION: ${{ github.sha }}

      - name: Build Docker image
        run: |
          docker build -t us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ github.sha }} .
          docker build -t us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:latest .

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker for Artifact Registry
        run: gcloud auth configure-docker us-central1-docker.pkg.dev

      - name: Push Docker image
        run: |
          docker push us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ github.sha }}
          docker push us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:latest

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.service_name }}-artifacts-${{ github.sha }}
          path: |
            dist/
            target/release/
            build/
          retention-days: 30

      - name: Generate SBOM
        uses: anchore/sbom-action@v0
        with:
          image: us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ github.sha }}
          format: spdx-json
          output-file: ${{ inputs.service_name }}-sbom.spdx.json

      - name: Upload SBOM
        uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.service_name }}-sbom-${{ github.sha }}
          path: ${{ inputs.service_name }}-sbom.spdx.json

  vulnerability-scan:
    runs-on: ubuntu-latest
    needs: quality-checks
    steps:
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ github.sha }}
          format: sarif
          output: trivy-results.sarif

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: trivy-results.sarif

      - name: Fail on critical vulnerabilities
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ github.sha }}
          exit-code: 1
          severity: CRITICAL,HIGH
