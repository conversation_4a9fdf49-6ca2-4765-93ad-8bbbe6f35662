#!/bin/bash
# Architecture Validation Script for CCL Platform
# This script validates that services follow CCL architecture guidelines

set -euo pipefail

SERVICE_NAME=${1:-}
LANGUAGE=${2:-}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[VALIDATE]${NC} $1"
}

success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Validation results
VALIDATION_ERRORS=0
VALIDATION_WARNINGS=0

# Increment error counter
fail_validation() {
    error "$1"
    ((VALIDATION_ERRORS++))
}

# Increment warning counter
warn_validation() {
    warning "$1"
    ((VALIDATION_WARNINGS++))
}

# Validate inputs
validate_inputs() {
    if [[ -z "$SERVICE_NAME" || -z "$LANGUAGE" ]]; then
        echo "Usage: $0 <service_name> <language>"
        echo "Example: $0 analysis-engine rust"
        exit 1
    fi
    
    # Validate service name
    case "$SERVICE_NAME" in
        analysis-engine|query-intelligence|pattern-mining|marketplace|web|collaboration|sdk)
            ;;
        *)
            fail_validation "Invalid service name: $SERVICE_NAME"
            ;;
    esac
    
    # Validate language
    case "$LANGUAGE" in
        rust|python|go|typescript)
            ;;
        *)
            fail_validation "Invalid language: $LANGUAGE"
            ;;
    esac
}

# Validate directory structure
validate_directory_structure() {
    log "Validating directory structure for $SERVICE_NAME ($LANGUAGE)"
    
    # Check if service directory exists
    if [[ ! -d "$SERVICE_NAME" ]]; then
        fail_validation "Service directory '$SERVICE_NAME' does not exist"
        return
    fi
    
    cd "$SERVICE_NAME"
    
    # Common required files
    local required_files=(
        "Makefile"
        "Dockerfile"
        "README.md"
        ".gitignore"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            fail_validation "Missing required file: $file"
        else
            success "Found required file: $file"
        fi
    done
    
    # Language-specific structure validation
    case "$LANGUAGE" in
        rust)
            validate_rust_structure
            ;;
        python)
            validate_python_structure
            ;;
        go)
            validate_go_structure
            ;;
        typescript)
            validate_typescript_structure
            ;;
    esac
    
    cd ..
}

# Validate Rust project structure
validate_rust_structure() {
    local required_files=(
        "Cargo.toml"
        "src/main.rs"
    )
    
    local recommended_dirs=(
        "src"
        "tests"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            fail_validation "Missing Rust file: $file"
        else
            success "Found Rust file: $file"
        fi
    done
    
    for dir in "${recommended_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            warn_validation "Missing recommended directory: $dir"
        else
            success "Found directory: $dir"
        fi
    done
    
    # Check Cargo.toml structure
    if [[ -f "Cargo.toml" ]]; then
        if grep -q "edition = \"2021\"" Cargo.toml; then
            success "Using Rust 2021 edition"
        else
            warn_validation "Not using Rust 2021 edition"
        fi
        
        if grep -q "\[dependencies\]" Cargo.toml; then
            success "Dependencies section found"
        else
            warn_validation "No dependencies section in Cargo.toml"
        fi
    fi
}

# Validate Python project structure
validate_python_structure() {
    local required_files=(
        "requirements.txt"
        "setup.py"
    )
    
    local recommended_files=(
        "pyproject.toml"
        "requirements-dev.txt"
        ".python-version"
    )
    
    local recommended_dirs=(
        "tests"
        "${SERVICE_NAME//-/_}"  # Convert kebab-case to snake_case
    )
    
    # Check for either requirements.txt or pyproject.toml
    if [[ ! -f "requirements.txt" && ! -f "pyproject.toml" ]]; then
        fail_validation "Missing dependency file (requirements.txt or pyproject.toml)"
    else
        success "Found dependency configuration"
    fi
    
    for file in "${recommended_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            warn_validation "Missing recommended file: $file"
        else
            success "Found recommended file: $file"
        fi
    done
    
    for dir in "${recommended_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            warn_validation "Missing recommended directory: $dir"
        else
            success "Found directory: $dir"
        fi
    done
    
    # Check Python version
    if [[ -f ".python-version" ]]; then
        local python_version=$(cat .python-version)
        if [[ "$python_version" =~ ^3\.(11|12) ]]; then
            success "Using supported Python version: $python_version"
        else
            warn_validation "Using unsupported Python version: $python_version"
        fi
    fi
}

# Validate Go project structure
validate_go_structure() {
    local required_files=(
        "go.mod"
    )
    
    local recommended_dirs=(
        "cmd"
        "internal"
        "pkg"
        "api"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            fail_validation "Missing Go file: $file"
        else
            success "Found Go file: $file"
        fi
    done
    
    for dir in "${recommended_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            warn_validation "Missing recommended directory: $dir"
        else
            success "Found directory: $dir"
        fi
    done
    
    # Check Go version
    if [[ -f "go.mod" ]]; then
        local go_version=$(grep "^go " go.mod | cut -d' ' -f2)
        if [[ "$go_version" =~ ^1\.(21|22) ]]; then
            success "Using supported Go version: $go_version"
        else
            warn_validation "Using unsupported Go version: $go_version"
        fi
    fi
}

# Validate TypeScript project structure
validate_typescript_structure() {
    local required_files=(
        "package.json"
        "tsconfig.json"
    )
    
    local recommended_files=(
        "package-lock.json"
        ".nvmrc"
        "jest.config.js"
        "eslint.config.js"
    )
    
    local recommended_dirs=(
        "src"
        "tests"
        "dist"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            fail_validation "Missing TypeScript file: $file"
        else
            success "Found TypeScript file: $file"
        fi
    done
    
    for file in "${recommended_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            warn_validation "Missing recommended file: $file"
        else
            success "Found recommended file: $file"
        fi
    done
    
    for dir in "${recommended_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            warn_validation "Missing recommended directory: $dir"
        else
            success "Found directory: $dir"
        fi
    done
    
    # Check Node.js version
    if [[ -f ".nvmrc" ]]; then
        local node_version=$(cat .nvmrc)
        if [[ "$node_version" =~ ^(18|20|21) ]]; then
            success "Using supported Node.js version: $node_version"
        else
            warn_validation "Using unsupported Node.js version: $node_version"
        fi
    fi
}

# Validate Makefile
validate_makefile() {
    log "Validating Makefile"
    
    if [[ ! -f "$SERVICE_NAME/Makefile" ]]; then
        fail_validation "Makefile not found"
        return
    fi
    
    cd "$SERVICE_NAME"
    
    # Required targets
    local required_targets=(
        "deps"
        "lint"
        "test"
        "build"
        "clean"
    )
    
    for target in "${required_targets[@]}"; do
        if grep -q "^${target}:" Makefile; then
            success "Found required target: $target"
        else
            fail_validation "Missing required target: $target"
        fi
    done
    
    # Check if common.mk is included
    if grep -q "include.*common.mk" Makefile; then
        success "Includes common.mk"
    else
        fail_validation "Does not include common.mk"
    fi
    
    # Check if LANGUAGE is set
    if grep -q "LANGUAGE.*:=.*$LANGUAGE" Makefile; then
        success "LANGUAGE variable set correctly"
    else
        fail_validation "LANGUAGE variable not set or incorrect"
    fi
    
    cd ..
}

# Validate Dockerfile
validate_dockerfile() {
    log "Validating Dockerfile"
    
    if [[ ! -f "$SERVICE_NAME/Dockerfile" ]]; then
        fail_validation "Dockerfile not found"
        return
    fi
    
    cd "$SERVICE_NAME"
    
    # Security checks
    if grep -q "USER.*root" Dockerfile; then
        fail_validation "Running as root user"
    elif grep -q "USER" Dockerfile; then
        success "Running as non-root user"
    else
        warn_validation "No USER directive found"
    fi
    
    # Multi-stage build check
    if grep -q "FROM.*AS" Dockerfile; then
        success "Using multi-stage build"
    else
        warn_validation "Not using multi-stage build"
    fi
    
    # Health check
    if grep -q "HEALTHCHECK" Dockerfile; then
        success "Health check defined"
    else
        warn_validation "No health check defined"
    fi
    
    # Base image check
    if grep -q "FROM.*:latest" Dockerfile; then
        fail_validation "Using :latest tag for base image"
    else
        success "Using specific version for base image"
    fi
    
    cd ..
}

# Validate service boundaries
validate_service_boundaries() {
    log "Validating service boundaries"
    
    cd "$SERVICE_NAME"
    
    # Check for cross-service dependencies
    case "$LANGUAGE" in
        rust)
            if [[ -f "Cargo.toml" ]]; then
                # Check for internal dependencies
                if grep -q "path.*\.\./.*" Cargo.toml; then
                    warn_validation "Found cross-service dependencies in Cargo.toml"
                fi
            fi
            ;;
        python)
            if [[ -f "requirements.txt" ]]; then
                # Check for relative path dependencies
                if grep -q "file://" requirements.txt || grep -q "\.\./.*" requirements.txt; then
                    warn_validation "Found cross-service dependencies in requirements.txt"
                fi
            fi
            ;;
        go)
            if [[ -f "go.mod" ]]; then
                # Check for replace directives pointing to other services
                if grep -q "replace.*\.\./.*" go.mod; then
                    warn_validation "Found cross-service dependencies in go.mod"
                fi
            fi
            ;;
        typescript)
            if [[ -f "package.json" ]]; then
                # Check for file: dependencies
                if grep -q "\"file:" package.json; then
                    warn_validation "Found cross-service dependencies in package.json"
                fi
            fi
            ;;
    esac
    
    cd ..
}

# Validate naming conventions
validate_naming_conventions() {
    log "Validating naming conventions"
    
    # Service name should be kebab-case
    if [[ "$SERVICE_NAME" =~ ^[a-z]+(-[a-z]+)*$ ]]; then
        success "Service name follows kebab-case convention"
    else
        fail_validation "Service name should be kebab-case"
    fi
    
    cd "$SERVICE_NAME"
    
    # Check file naming conventions
    case "$LANGUAGE" in
        rust)
            # Rust files should be snake_case
            if find src -name "*.rs" | grep -v "^src/[a-z_]*\.rs$" | head -1; then
                warn_validation "Some Rust files don't follow snake_case convention"
            else
                success "Rust files follow snake_case convention"
            fi
            ;;
        python)
            # Python files should be snake_case
            if find . -name "*.py" | grep -v "^[a-z_]*\.py$" | head -1; then
                warn_validation "Some Python files don't follow snake_case convention"
            else
                success "Python files follow snake_case convention"
            fi
            ;;
        go)
            # Go files should be snake_case
            if find . -name "*.go" | grep -v "^[a-z_]*\.go$" | head -1; then
                warn_validation "Some Go files don't follow snake_case convention"
            else
                success "Go files follow snake_case convention"
            fi
            ;;
        typescript)
            # TypeScript files can be camelCase or kebab-case
            success "TypeScript naming conventions vary by project"
            ;;
    esac
    
    cd ..
}

# Main validation function
main() {
    log "Starting architecture validation for $SERVICE_NAME ($LANGUAGE)"
    
    validate_inputs
    validate_directory_structure
    validate_makefile
    validate_dockerfile
    validate_service_boundaries
    validate_naming_conventions
    
    # Summary
    echo ""
    log "Validation Summary:"
    
    if [[ $VALIDATION_ERRORS -eq 0 ]]; then
        success "All critical validations passed"
    else
        error "$VALIDATION_ERRORS critical validation(s) failed"
    fi
    
    if [[ $VALIDATION_WARNINGS -gt 0 ]]; then
        warning "$VALIDATION_WARNINGS warning(s) found"
    fi
    
    echo ""
    
    # Exit with error if critical validations failed
    if [[ $VALIDATION_ERRORS -gt 0 ]]; then
        error "Architecture validation failed"
        exit 1
    else
        success "Architecture validation passed"
        exit 0
    fi
}

# Run main function
main "$@"
