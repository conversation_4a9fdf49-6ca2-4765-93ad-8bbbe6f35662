#!/bin/bash
# Security Validation Script for CCL Platform
# This script validates security compliance for services

set -euo pipefail

SERVICE_NAME=${1:-}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[SECURITY]${NC} $1"
}

success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Validation results
SECURITY_ERRORS=0
SECURITY_WARNINGS=0

# Increment error counter
fail_security() {
    error "$1"
    ((SECURITY_ERRORS++))
}

# Increment warning counter
warn_security() {
    warning "$1"
    ((SECURITY_WARNINGS++))
}

# Validate inputs
validate_inputs() {
    if [[ -z "$SERVICE_NAME" ]]; then
        echo "Usage: $0 <service_name>"
        echo "Example: $0 analysis-engine"
        exit 1
    fi
    
    if [[ ! -d "$SERVICE_NAME" ]]; then
        fail_security "Service directory '$SERVICE_NAME' does not exist"
        exit 1
    fi
}

# Check for hardcoded secrets
check_hardcoded_secrets() {
    log "Checking for hardcoded secrets in $SERVICE_NAME"
    
    cd "$SERVICE_NAME"
    
    # Common secret patterns
    local secret_patterns=(
        "password\s*=\s*['\"][^'\"]{8,}['\"]"
        "api[_-]?key\s*=\s*['\"][^'\"]{16,}['\"]"
        "secret\s*=\s*['\"][^'\"]{16,}['\"]"
        "token\s*=\s*['\"][^'\"]{16,}['\"]"
        "AKIA[0-9A-Z]{16}"  # AWS Access Key
        "sk_live_[0-9a-zA-Z]{24}"  # Stripe Live Key
        "sk_test_[0-9a-zA-Z]{24}"  # Stripe Test Key
    )
    
    local secrets_found=false
    
    for pattern in "${secret_patterns[@]}"; do
        if grep -r -E "$pattern" . --exclude-dir=.git --exclude-dir=node_modules --exclude-dir=target 2>/dev/null; then
            fail_security "Potential hardcoded secret found: $pattern"
            secrets_found=true
        fi
    done
    
    if [[ "$secrets_found" == false ]]; then
        success "No hardcoded secrets detected"
    fi
    
    cd ..
}

# Check dependency vulnerabilities
check_dependency_vulnerabilities() {
    log "Checking dependency vulnerabilities for $SERVICE_NAME"
    
    cd "$SERVICE_NAME"
    
    # Determine language and run appropriate scanner
    if [[ -f "Cargo.toml" ]]; then
        check_rust_vulnerabilities
    elif [[ -f "requirements.txt" || -f "pyproject.toml" ]]; then
        check_python_vulnerabilities
    elif [[ -f "go.mod" ]]; then
        check_go_vulnerabilities
    elif [[ -f "package.json" ]]; then
        check_typescript_vulnerabilities
    else
        warn_security "Could not determine language for vulnerability scanning"
    fi
    
    cd ..
}

# Check Rust vulnerabilities
check_rust_vulnerabilities() {
    if command -v cargo-audit >/dev/null 2>&1; then
        log "Running cargo audit"
        if cargo audit --json > audit-report.json 2>/dev/null; then
            local vuln_count=$(jq '.vulnerabilities.count' audit-report.json 2>/dev/null || echo "0")
            if [[ "$vuln_count" -gt 0 ]]; then
                fail_security "Found $vuln_count Rust vulnerabilities"
                jq '.vulnerabilities.list[] | "\(.advisory.id): \(.advisory.title)"' audit-report.json 2>/dev/null || true
            else
                success "No Rust vulnerabilities found"
            fi
        else
            warn_security "Failed to run cargo audit"
        fi
    else
        warn_security "cargo-audit not installed"
    fi
}

# Check Python vulnerabilities
check_python_vulnerabilities() {
    if command -v safety >/dev/null 2>&1; then
        log "Running safety check"
        if safety check --json --output safety-report.json 2>/dev/null; then
            success "No Python vulnerabilities found"
        else
            local vuln_count=$(jq '. | length' safety-report.json 2>/dev/null || echo "0")
            if [[ "$vuln_count" -gt 0 ]]; then
                fail_security "Found $vuln_count Python vulnerabilities"
                jq '.[] | "\(.vulnerability_id): \(.advisory)"' safety-report.json 2>/dev/null || true
            fi
        fi
    else
        warn_security "safety not installed"
    fi
}

# Check Go vulnerabilities
check_go_vulnerabilities() {
    if command -v govulncheck >/dev/null 2>&1; then
        log "Running govulncheck"
        if govulncheck -json ./... > govuln-report.json 2>/dev/null; then
            local vuln_count=$(jq '[.[] | select(.finding)] | length' govuln-report.json 2>/dev/null || echo "0")
            if [[ "$vuln_count" -gt 0 ]]; then
                fail_security "Found $vuln_count Go vulnerabilities"
                jq '.[] | select(.finding) | "\(.finding.osv): \(.finding.summary)"' govuln-report.json 2>/dev/null || true
            else
                success "No Go vulnerabilities found"
            fi
        else
            warn_security "Failed to run govulncheck"
        fi
    else
        warn_security "govulncheck not installed"
    fi
}

# Check TypeScript vulnerabilities
check_typescript_vulnerabilities() {
    if command -v npm >/dev/null 2>&1; then
        log "Running npm audit"
        if npm audit --audit-level=moderate --json > npm-audit.json 2>/dev/null; then
            success "No TypeScript vulnerabilities found"
        else
            local vuln_count=$(jq '.metadata.vulnerabilities.total' npm-audit.json 2>/dev/null || echo "0")
            if [[ "$vuln_count" -gt 0 ]]; then
                fail_security "Found $vuln_count npm vulnerabilities"
                jq '.advisories | to_entries[] | "\(.value.id): \(.value.title)"' npm-audit.json 2>/dev/null || true
            fi
        fi
    else
        warn_security "npm not installed"
    fi
}

# Check container security
check_container_security() {
    log "Checking container security for $SERVICE_NAME"
    
    cd "$SERVICE_NAME"
    
    if [[ ! -f "Dockerfile" ]]; then
        warn_security "No Dockerfile found"
        cd ..
        return
    fi
    
    # Check for security best practices in Dockerfile
    
    # Check if running as root
    if grep -q "USER root" Dockerfile; then
        fail_security "Container runs as root user"
    elif grep -q "USER" Dockerfile; then
        success "Container runs as non-root user"
    else
        warn_security "No USER directive found in Dockerfile"
    fi
    
    # Check for latest tags
    if grep -q ":latest" Dockerfile; then
        fail_security "Using :latest tag in Dockerfile"
    else
        success "Using specific versions in Dockerfile"
    fi
    
    # Check for ADD vs COPY
    if grep -q "^ADD" Dockerfile; then
        warn_security "Using ADD instead of COPY (potential security risk)"
    fi
    
    # Check for HEALTHCHECK
    if grep -q "HEALTHCHECK" Dockerfile; then
        success "Health check defined"
    else
        warn_security "No health check defined"
    fi
    
    # Check for secrets in build args
    if grep -E "(PASSWORD|SECRET|KEY|TOKEN)" Dockerfile; then
        fail_security "Potential secrets in Dockerfile build args"
    fi
    
    cd ..
}

# Check file permissions
check_file_permissions() {
    log "Checking file permissions for $SERVICE_NAME"
    
    cd "$SERVICE_NAME"
    
    # Check for world-writable files
    local writable_files=$(find . -type f -perm -002 2>/dev/null || true)
    if [[ -n "$writable_files" ]]; then
        fail_security "World-writable files found:"
        echo "$writable_files"
    else
        success "No world-writable files found"
    fi
    
    # Check for executable scripts without proper shebang
    local scripts=$(find . -type f -executable -name "*.sh" 2>/dev/null || true)
    for script in $scripts; do
        if [[ -f "$script" ]] && ! head -1 "$script" | grep -q "^#!"; then
            warn_security "Script without shebang: $script"
        fi
    done
    
    cd ..
}

# Check for security configuration files
check_security_configs() {
    log "Checking security configuration files for $SERVICE_NAME"
    
    cd "$SERVICE_NAME"
    
    # Check for .gitleaks.toml
    if [[ -f ".gitleaks.toml" ]]; then
        success "GitLeaks configuration found"
    else
        warn_security "No GitLeaks configuration found"
    fi
    
    # Check for security scanning configs
    local security_configs=(
        ".trivyignore"
        "bandit.yaml"
        ".bandit"
        "gosec.json"
    )
    
    for config in "${security_configs[@]}"; do
        if [[ -f "$config" ]]; then
            success "Security config found: $config"
        fi
    done
    
    cd ..
}

# Check environment variable usage
check_environment_variables() {
    log "Checking environment variable usage for $SERVICE_NAME"
    
    cd "$SERVICE_NAME"
    
    # Look for potential secrets in environment variables
    local env_patterns=(
        "PASSWORD"
        "SECRET"
        "KEY"
        "TOKEN"
        "CREDENTIAL"
    )
    
    for pattern in "${env_patterns[@]}"; do
        if grep -r "os\.getenv.*$pattern\|process\.env\.$pattern\|std::env::var.*$pattern" . 2>/dev/null; then
            success "Using environment variables for secrets: $pattern"
        fi
    done
    
    # Check for hardcoded environment values
    if grep -r "export.*=.*['\"][^'\"]*['\"]" . 2>/dev/null | grep -E "(PASSWORD|SECRET|KEY|TOKEN)"; then
        fail_security "Hardcoded secrets in environment variable exports"
    fi
    
    cd ..
}

# Check TLS/SSL configuration
check_tls_configuration() {
    log "Checking TLS configuration for $SERVICE_NAME"
    
    cd "$SERVICE_NAME"
    
    # Look for TLS configuration
    if grep -r -i "tls\|ssl" . --include="*.rs" --include="*.py" --include="*.go" --include="*.ts" --include="*.js" 2>/dev/null; then
        # Check for insecure TLS configurations
        if grep -r -i "verify.*false\|insecure.*true" . 2>/dev/null; then
            fail_security "Insecure TLS configuration found"
        else
            success "TLS configuration appears secure"
        fi
    fi
    
    cd ..
}

# Check for logging security
check_logging_security() {
    log "Checking logging security for $SERVICE_NAME"
    
    cd "$SERVICE_NAME"
    
    # Look for potential secret logging
    if grep -r -E "log.*password\|print.*secret\|console\.log.*token" . 2>/dev/null; then
        fail_security "Potential secret logging detected"
    else
        success "No obvious secret logging found"
    fi
    
    cd ..
}

# Generate security report
generate_security_report() {
    log "Generating security report for $SERVICE_NAME"
    
    local report_file="security-report-${SERVICE_NAME}.json"
    
    cat > "$report_file" << EOF
{
  "service": "$SERVICE_NAME",
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "security_errors": $SECURITY_ERRORS,
  "security_warnings": $SECURITY_WARNINGS,
  "status": "$([ $SECURITY_ERRORS -eq 0 ] && echo "PASS" || echo "FAIL")"
}
EOF
    
    success "Security report generated: $report_file"
}

# Main validation function
main() {
    log "Starting security validation for $SERVICE_NAME"
    
    validate_inputs
    check_hardcoded_secrets
    check_dependency_vulnerabilities
    check_container_security
    check_file_permissions
    check_security_configs
    check_environment_variables
    check_tls_configuration
    check_logging_security
    generate_security_report
    
    # Summary
    echo ""
    log "Security Validation Summary:"
    
    if [[ $SECURITY_ERRORS -eq 0 ]]; then
        success "All security validations passed"
    else
        error "$SECURITY_ERRORS security issue(s) found"
    fi
    
    if [[ $SECURITY_WARNINGS -gt 0 ]]; then
        warning "$SECURITY_WARNINGS security warning(s) found"
    fi
    
    echo ""
    
    # Exit with error if security issues found
    if [[ $SECURITY_ERRORS -gt 0 ]]; then
        error "Security validation failed"
        exit 1
    else
        success "Security validation passed"
        exit 0
    fi
}

# Run main function
main "$@"
