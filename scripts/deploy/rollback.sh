#!/bin/bash
# Rollback Script for CCL Platform
# This script provides emergency rollback capabilities for deployed services

set -euo pipefail

# Configuration
SERVICE=${1:-}
ENVIRONMENT=${2:-}
TARGET_VERSION=${3:-"LATEST"}
PROJECT_ID=${PROJECT_ID:-ccl-platform}
REGION=${REGION:-us-central1}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Show usage
show_usage() {
    echo "Usage: $0 <service> <environment> [target_version]"
    echo ""
    echo "Arguments:"
    echo "  service         Service name (analysis-engine, query-intelligence, etc.)"
    echo "  environment     Target environment (development, staging, production)"
    echo "  target_version  Version to rollback to (default: LATEST)"
    echo ""
    echo "Examples:"
    echo "  $0 analysis-engine production                    # Rollback to previous version"
    echo "  $0 marketplace staging v1.2.3                   # Rollback to specific version"
    echo "  $0 query-intelligence development LATEST        # Rollback to latest stable"
}

# Validation
validate_inputs() {
    if [[ -z "$SERVICE" || -z "$ENVIRONMENT" ]]; then
        error "Missing required arguments"
        show_usage
        exit 1
    fi
    
    # Validate service name
    case "$SERVICE" in
        analysis-engine|query-intelligence|pattern-mining|marketplace|web|collaboration)
            ;;
        *)
            error "Invalid service: $SERVICE"
            error "Valid services: analysis-engine, query-intelligence, pattern-mining, marketplace, web, collaboration"
            exit 1
            ;;
    esac
    
    # Validate environment
    case "$ENVIRONMENT" in
        development|staging|production)
            ;;
        *)
            error "Invalid environment: $ENVIRONMENT"
            error "Valid environments: development, staging, production"
            exit 1
            ;;
    esac
    
    # Set project ID based on environment
    case "$ENVIRONMENT" in
        development)
            PROJECT_ID="ccl-platform-dev"
            ;;
        staging)
            PROJECT_ID="ccl-platform-staging"
            ;;
        production)
            PROJECT_ID="ccl-platform-prod"
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        error "gcloud CLI is not installed"
        exit 1
    fi
    
    # Check authentication
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        error "Not authenticated with gcloud. Run 'gcloud auth login'"
        exit 1
    fi
    
    # Set project
    gcloud config set project "$PROJECT_ID"
    
    success "Prerequisites check passed"
}

# Get current service status
get_current_status() {
    log "Getting current service status..."
    
    # Get current traffic allocation
    local traffic_info=$(gcloud run services describe "$SERVICE" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="table(status.traffic[].revisionName,status.traffic[].percent,status.traffic[].tag)")
    
    echo "Current traffic allocation:"
    echo "$traffic_info"
    
    # Get current revision
    CURRENT_REVISION=$(gcloud run services describe "$SERVICE" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="value(status.traffic[0].revisionName)")
    
    log "Current serving revision: $CURRENT_REVISION"
}

# List available revisions
list_available_revisions() {
    log "Available revisions for rollback:"
    
    gcloud run revisions list \
        --service="$SERVICE" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="table(metadata.name,metadata.creationTimestamp,status.conditions[0].status)" \
        --sort-by="~metadata.creationTimestamp" \
        --limit=10
}

# Confirm rollback
confirm_rollback() {
    if [[ "$ENVIRONMENT" == "production" ]]; then
        warning "You are about to rollback a PRODUCTION service!"
        warning "Service: $SERVICE"
        warning "Environment: $ENVIRONMENT"
        warning "Target: $TARGET_VERSION"
        warning "Current revision: $CURRENT_REVISION"
        
        echo ""
        read -p "Are you sure you want to proceed? (type 'yes' to confirm): " confirmation
        
        if [[ "$confirmation" != "yes" ]]; then
            log "Rollback cancelled by user"
            exit 0
        fi
    fi
}

# Perform rollback
perform_rollback() {
    log "Starting rollback process..."
    
    # Record rollback start
    record_rollback "STARTED"
    
    if [[ "$TARGET_VERSION" == "LATEST" ]]; then
        # Rollback to previous revision
        log "Rolling back to previous revision..."
        
        gcloud run services update-traffic "$SERVICE" \
            --to-revisions=LATEST=100 \
            --region="$REGION" \
            --project="$PROJECT_ID"
    else
        # Rollback to specific version
        log "Rolling back to version: $TARGET_VERSION"
        
        # Check if the target revision exists
        if ! gcloud run revisions describe "$SERVICE-$TARGET_VERSION" \
            --region="$REGION" \
            --project="$PROJECT_ID" &> /dev/null; then
            error "Target revision does not exist: $SERVICE-$TARGET_VERSION"
            exit 1
        fi
        
        gcloud run services update-traffic "$SERVICE" \
            --to-revisions="$SERVICE-$TARGET_VERSION=100" \
            --region="$REGION" \
            --project="$PROJECT_ID"
    fi
    
    success "Traffic rollback completed"
}

# Verify rollback
verify_rollback() {
    log "Verifying rollback..."
    
    # Wait for rollback to take effect
    sleep 30
    
    # Check service health
    local service_url=$(gcloud run services describe "$SERVICE" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="value(status.url)")
    
    log "Checking service health at: $service_url/health"
    
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$service_url/health" > /dev/null; then
            success "Health check passed (attempt $attempt)"
            break
        else
            warning "Health check failed (attempt $attempt/$max_attempts)"
            if [[ $attempt -eq $max_attempts ]]; then
                error "Health check failed after $max_attempts attempts"
                record_rollback "FAILED"
                exit 1
            fi
            sleep 10
            ((attempt++))
        fi
    done
    
    # Monitor for errors
    log "Monitoring for errors (60 seconds)..."
    sleep 60
    
    local error_count=$(gcloud logging read \
        "resource.type=cloud_run_revision AND resource.labels.service_name=$SERVICE AND severity>=ERROR" \
        --limit=50 \
        --format="value(timestamp)" \
        --freshness=2m \
        --project="$PROJECT_ID" | wc -l)
    
    if [[ $error_count -gt 5 ]]; then
        warning "High error rate detected after rollback: $error_count errors"
        warning "Manual investigation may be required"
    else
        success "Error rate is acceptable: $error_count errors"
    fi
}

# Record rollback
record_rollback() {
    local status=$1
    local rollback_id=$(uuidgen)
    
    log "Recording rollback: $status"
    
    # Create rollback record
    echo "ROLLBACK,$SERVICE,$TARGET_VERSION,$ENVIRONMENT,$status,$(date -u +%Y-%m-%dT%H:%M:%SZ),$rollback_id" >> rollbacks.csv
    
    # Upload to Cloud Storage (if configured)
    if gsutil ls "gs://ccl-deployments/history/" &> /dev/null; then
        gsutil cp rollbacks.csv "gs://ccl-deployments/history/"
    fi
}

# Send notification
send_notification() {
    local status=$1
    local message=$2
    
    log "Notification: [$status] $message"
    
    # Example: Send to Slack (implement based on your setup)
    # curl -X POST -H 'Content-type: application/json' \
    #     --data "{\"text\":\"[$status] $message\"}" \
    #     "$SLACK_WEBHOOK_URL"
    
    # Example: Send to PagerDuty for production
    if [[ "$ENVIRONMENT" == "production" && "$status" == "SUCCESS" ]]; then
        log "Sending PagerDuty notification for production rollback"
        # Implement PagerDuty integration
    fi
}

# Cleanup after rollback
cleanup_after_rollback() {
    log "Performing post-rollback cleanup..."
    
    # Remove any failed revision tags
    local failed_tags=$(gcloud run services describe "$SERVICE" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="value(status.traffic[].tag)" | grep -v "^$" || true)
    
    for tag in $failed_tags; do
        if [[ "$tag" != "latest" && "$tag" != "$TARGET_VERSION" ]]; then
            log "Removing tag: $tag"
            gcloud run services update-traffic "$SERVICE" \
                --remove-tags="$tag" \
                --region="$REGION" \
                --project="$PROJECT_ID" || true
        fi
    done
    
    success "Cleanup completed"
}

# Main rollback process
main() {
    log "Starting rollback for $SERVICE in $ENVIRONMENT"
    
    validate_inputs
    check_prerequisites
    get_current_status
    list_available_revisions
    confirm_rollback
    
    # Perform rollback
    perform_rollback
    verify_rollback
    cleanup_after_rollback
    
    # Record successful rollback
    record_rollback "SUCCESS"
    send_notification "SUCCESS" "Rollback of $SERVICE to $TARGET_VERSION in $ENVIRONMENT completed successfully"
    
    success "Rollback completed successfully!"
    
    # Show final status
    log "Final service status:"
    get_current_status
}

# Handle script interruption
trap 'error "Rollback script interrupted"; exit 1' INT TERM

# Show help if no arguments
if [[ $# -eq 0 ]]; then
    show_usage
    exit 1
fi

# Run main function
main "$@"
