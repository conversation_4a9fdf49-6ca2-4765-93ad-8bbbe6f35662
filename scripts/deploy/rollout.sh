#!/bin/bash
# Progressive Rollout Script for CCL Platform
# This script implements canary deployments with automated monitoring and rollback

set -euo pipefail

# Configuration
SERVICE=${1:-}
VERSION=${2:-}
ENVIRONMENT=${3:-}
INITIAL_PERCENTAGE=${4:-10}
PROJECT_ID=${PROJECT_ID:-ccl-platform}
REGION=${REGION:-us-central1}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Validation
validate_inputs() {
    if [[ -z "$SERVICE" || -z "$VERSION" || -z "$ENVIRONMENT" ]]; then
        error "Usage: $0 <service> <version> <environment> [initial_percentage]"
        error "Example: $0 analysis-engine v1.2.3 production 10"
        exit 1
    fi
    
    # Validate service name
    case "$SERVICE" in
        analysis-engine|query-intelligence|pattern-mining|marketplace|web|collaboration)
            ;;
        *)
            error "Invalid service: $SERVICE"
            error "Valid services: analysis-engine, query-intelligence, pattern-mining, marketplace, web, collaboration"
            exit 1
            ;;
    esac
    
    # Validate environment
    case "$ENVIRONMENT" in
        development|staging|production)
            ;;
        *)
            error "Invalid environment: $ENVIRONMENT"
            error "Valid environments: development, staging, production"
            exit 1
            ;;
    esac
    
    # Set project ID based on environment
    case "$ENVIRONMENT" in
        development)
            PROJECT_ID="ccl-platform-dev"
            ;;
        staging)
            PROJECT_ID="ccl-platform-staging"
            ;;
        production)
            PROJECT_ID="ccl-platform-prod"
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        error "gcloud CLI is not installed"
        exit 1
    fi
    
    # Check authentication
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        error "Not authenticated with gcloud. Run 'gcloud auth login'"
        exit 1
    fi
    
    # Set project
    gcloud config set project "$PROJECT_ID"
    
    # Verify image exists
    log "Verifying image exists..."
    if ! gcloud container images describe "us-central1-docker.pkg.dev/$PROJECT_ID/services/$SERVICE:$VERSION" &> /dev/null; then
        error "Image does not exist: us-central1-docker.pkg.dev/$PROJECT_ID/services/$SERVICE:$VERSION"
        exit 1
    fi
    
    success "Prerequisites check passed"
}

# Deploy new revision without traffic
deploy_new_revision() {
    log "Deploying new revision without traffic..."
    
    gcloud run deploy "$SERVICE" \
        --image="us-central1-docker.pkg.dev/$PROJECT_ID/services/$SERVICE:$VERSION" \
        --no-traffic \
        --tag="$VERSION" \
        --region="$REGION" \
        --platform=managed \
        --memory=2Gi \
        --cpu=2 \
        --max-instances=100 \
        --min-instances=1 \
        --set-env-vars="VERSION=$VERSION,ENVIRONMENT=$ENVIRONMENT" \
        --project="$PROJECT_ID"
    
    success "New revision deployed without traffic"
}

# Shift traffic gradually
shift_traffic() {
    local percentage=$1
    log "Shifting $percentage% traffic to new revision..."
    
    gcloud run services update-traffic "$SERVICE" \
        --to-tags="$VERSION=$percentage" \
        --region="$REGION" \
        --project="$PROJECT_ID"
    
    success "Traffic shifted to $percentage%"
}

# Monitor metrics for specified duration
monitor_metrics() {
    local duration_seconds=$1
    local percentage=$2
    
    log "Monitoring metrics for $duration_seconds seconds at $percentage% traffic..."
    
    local start_time=$(date +%s)
    local end_time=$((start_time + duration_seconds))
    
    while [[ $(date +%s) -lt $end_time ]]; do
        # Check error rate
        local error_count=$(gcloud logging read \
            "resource.type=cloud_run_revision AND resource.labels.service_name=$SERVICE AND severity>=ERROR" \
            --limit=100 \
            --format="value(timestamp)" \
            --freshness=2m \
            --project="$PROJECT_ID" | wc -l)
        
        # Check latency (simplified - in real implementation, use Cloud Monitoring API)
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        log "Monitoring progress: ${elapsed}s/${duration_seconds}s, Errors: $error_count"
        
        # Check if error rate is too high
        if [[ $error_count -gt 10 ]]; then
            error "High error rate detected: $error_count errors in 2 minutes"
            return 1
        fi
        
        sleep 30
    done
    
    success "Monitoring completed successfully"
    return 0
}

# Rollback to previous version
rollback() {
    error "Rolling back deployment..."
    
    # Get current traffic allocation
    local current_traffic=$(gcloud run services describe "$SERVICE" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="value(status.traffic[0].percent)")
    
    # Shift all traffic back to LATEST (previous version)
    gcloud run services update-traffic "$SERVICE" \
        --to-revisions=LATEST=100 \
        --region="$REGION" \
        --project="$PROJECT_ID"
    
    # Delete the failed revision tag
    gcloud run services update-traffic "$SERVICE" \
        --remove-tags="$VERSION" \
        --region="$REGION" \
        --project="$PROJECT_ID" || true
    
    error "Rollback completed"
    
    # Send notification
    send_notification "ROLLBACK" "Deployment of $SERVICE:$VERSION to $ENVIRONMENT was rolled back due to high error rate"
}

# Send notification (placeholder - implement with your notification system)
send_notification() {
    local status=$1
    local message=$2
    
    log "Notification: [$status] $message"
    
    # Example: Send to Slack (implement based on your setup)
    # curl -X POST -H 'Content-type: application/json' \
    #     --data "{\"text\":\"[$status] $message\"}" \
    #     "$SLACK_WEBHOOK_URL"
}

# Complete rollout
complete_rollout() {
    log "Completing rollout to 100%..."
    
    gcloud run services update-traffic "$SERVICE" \
        --to-tags="$VERSION=100" \
        --region="$REGION" \
        --project="$PROJECT_ID"
    
    # Clean up old revisions (keep last 5)
    log "Cleaning up old revisions..."
    gcloud run revisions list \
        --service="$SERVICE" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="value(metadata.name)" \
        --sort-by="~metadata.creationTimestamp" \
        --limit=100 | tail -n +6 | while read -r revision; do
        if [[ -n "$revision" ]]; then
            gcloud run revisions delete "$revision" \
                --region="$REGION" \
                --project="$PROJECT_ID" \
                --quiet || true
        fi
    done
    
    success "Rollout completed successfully"
}

# Record deployment
record_deployment() {
    local status=$1
    local deployment_id=$(uuidgen)
    
    log "Recording deployment: $status"
    
    # Create deployment record
    echo "$SERVICE,$VERSION,$ENVIRONMENT,$status,$(date -u +%Y-%m-%dT%H:%M:%SZ),$deployment_id" >> deployments.csv
    
    # Upload to Cloud Storage (if configured)
    if gsutil ls "gs://ccl-deployments/history/" &> /dev/null; then
        gsutil cp deployments.csv "gs://ccl-deployments/history/"
    fi
}

# Main rollout process
main() {
    log "Starting progressive rollout for $SERVICE:$VERSION to $ENVIRONMENT"
    
    validate_inputs
    check_prerequisites
    
    # Record deployment start
    record_deployment "STARTED"
    
    # Deploy new revision
    deploy_new_revision
    
    # Progressive rollout stages
    local stages=(10 25 50 100)
    local monitor_duration=300  # 5 minutes
    
    # Override stages for production
    if [[ "$ENVIRONMENT" == "production" ]]; then
        stages=(5 10 25 50 100)
        monitor_duration=600  # 10 minutes
    fi
    
    for stage in "${stages[@]}"; do
        if [[ $stage -eq 100 ]]; then
            # Final stage - complete rollout
            complete_rollout
            break
        fi
        
        # Shift traffic
        shift_traffic "$stage"
        
        # Monitor metrics
        if ! monitor_metrics "$monitor_duration" "$stage"; then
            rollback
            record_deployment "FAILED"
            send_notification "FAILED" "Deployment of $SERVICE:$VERSION to $ENVIRONMENT failed and was rolled back"
            exit 1
        fi
        
        success "Stage $stage% completed successfully"
    done
    
    # Record successful deployment
    record_deployment "SUCCESS"
    send_notification "SUCCESS" "Deployment of $SERVICE:$VERSION to $ENVIRONMENT completed successfully"
    
    success "Progressive rollout completed successfully!"
}

# Handle script interruption
trap 'error "Script interrupted"; rollback; exit 1' INT TERM

# Run main function
main "$@"
